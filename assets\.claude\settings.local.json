{"permissions": {"allow": ["<PERSON><PERSON>(dir:*)", "<PERSON><PERSON>(python:*)", "Bash(start index.html)", "Bash(start http://localhost:8000)", "<PERSON><PERSON>(explorer http://localhost:8000)", "<PERSON><PERSON>(powershell:*)", "Bash(git init:*)", "Bash(git add:*)", "Bash(git commit:*)", "Bash(node:*)", "Bash(wc:*)", "<PERSON><PERSON>(curl:*)", "Bash(grep:*)", "Bash(npx eslint:*)", "Bash(start index-clean.html)", "Bash(start \"C:\\Users\\<USER>\\OneDrive\\Documents\\CODES\\__ROSEBUD-AI-LABRUTE\\LaBrute Reborn - Arena Combat-Strengthen guaranteed hit and dodge feedback systems (1)\\index-clean.html\")", "<PERSON><PERSON>(explorer:*)", "Bash(start http://localhost:8000/index-clean.html)", "Bash(git checkout:*)"], "deny": []}}