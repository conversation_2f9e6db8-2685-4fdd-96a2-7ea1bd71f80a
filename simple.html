<!DOCTYPE html>
<html>
<head>
    <title>LaBrute Simple</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #2d2419;
        }
    </style>
</head>
<body>
    <div id="phaser-game-container"></div>
    
    <script>
        class FightScene extends Phaser.Scene {
            constructor() {
                super({ key: 'Fight' });
            }
            
            preload() {
                // Create simple colored rectangles as placeholders
                this.load.image('fighter1', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8z8DwHwAFBQIAX8jx0gAAAABJRU5ErkJggg==');
                this.load.image('fighter2', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
            }
            
            create() {
                // Background
                this.add.rectangle(512, 384, 1024, 768, 0x4a4a4a);
                
                // Ground
                this.add.rectangle(512, 600, 1024, 200, 0x3a3a3a);
                
                // Fighter 1
                this.fighter1 = this.add.rectangle(200, 500, 60, 80, 0xff6b35);
                this.fighter1.name = 'Fighter 1';
                
                // Fighter 2
                this.fighter2 = this.add.rectangle(824, 500, 60, 80, 0x4169e1);
                this.fighter2.name = 'Fighter 2';
                
                // Health bars
                this.add.rectangle(200, 50, 200, 20, 0x333333);
                this.fighter1.healthBar = this.add.rectangle(200, 50, 200, 20, 0x00ff00);
                
                this.add.rectangle(824, 50, 200, 20, 0x333333);
                this.fighter2.healthBar = this.add.rectangle(824, 50, 200, 20, 0x00ff00);
                
                // Names
                this.add.text(200, 80, 'Brute Alpha', { fontSize: '16px', fill: '#fff' }).setOrigin(0.5);
                this.add.text(824, 80, 'Brute Beta', { fontSize: '16px', fill: '#fff' }).setOrigin(0.5);
                
                // Combat log
                this.combatLog = this.add.text(512, 700, 'Combat Started!', { 
                    fontSize: '14px', 
                    fill: '#fff',
                    align: 'center'
                }).setOrigin(0.5);
                
                // Start combat
                this.time.addEvent({
                    delay: 1000,
                    callback: () => this.performAttack(),
                    loop: true
                });
                
                this.currentAttacker = this.fighter1;
                this.fighter1.health = 100;
                this.fighter2.health = 100;
            }
            
            performAttack() {
                const attacker = this.currentAttacker;
                const target = attacker === this.fighter1 ? this.fighter2 : this.fighter1;
                
                // Move attacker
                this.tweens.add({
                    targets: attacker,
                    x: target.x + (attacker === this.fighter1 ? -80 : 80),
                    duration: 200,
                    yoyo: true,
                    ease: 'Power1',
                    onYoyo: () => {
                        // Damage
                        const damage = Phaser.Math.Between(5, 15);
                        target.health -= damage;
                        
                        // Update health bar
                        target.healthBar.scaleX = Math.max(0, target.health / 100);
                        
                        // Camera shake
                        this.cameras.main.shake(100, 0.01);
                        
                        // Flash effect
                        this.tweens.add({
                            targets: target,
                            alpha: 0.5,
                            duration: 100,
                            yoyo: true
                        });
                        
                        // Update log
                        this.combatLog.setText(`${attacker.name} deals ${damage} damage!`);
                        
                        // Check for winner
                        if (target.health <= 0) {
                            this.combatLog.setText(`${attacker.name} WINS!`);
                            this.time.removeAllEvents();
                        }
                    }
                });
                
                // Switch attacker
                this.currentAttacker = target;
            }
        }
        
        const config = {
            type: Phaser.AUTO,
            width: 1024,
            height: 768,
            parent: 'phaser-game-container',
            backgroundColor: '#2d2419',
            scene: FightScene
        };
        
        const game = new Phaser.Game(config);
        console.log('Game started!');
    </script>
</body>
</html>