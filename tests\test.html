<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>LaBrute Test</title>
    <script type="importmap">
      {
        "imports": {
          "phaser": "https://esm.sh/phaser@3.70.0"
        }
      }
    </script>
    <style>
      body {
        margin: 0;
        padding: 0;
        background: #222;
        color: white;
        font-family: monospace;
      }
      #error-log {
        padding: 20px;
        white-space: pre-wrap;
      }
    </style>
</head>
<body>
    <div id="phaser-game-container"></div>
    <div id="error-log"></div>
    
    <script type="module">
        const errorLog = document.getElementById('error-log');
        
        window.onerror = function(msg, url, lineNo, columnNo, error) {
            const message = `Error: ${msg}\nFile: ${url}\nLine: ${lineNo}\nColumn: ${columnNo}\nStack: ${error?.stack || 'N/A'}`;
            console.error(message);
            errorLog.textContent += message + '\n\n';
            return false;
        };
        
        window.addEventListener('unhandledrejection', function(event) {
            const message = `Unhandled Promise Rejection: ${event.reason}`;
            console.error(message);
            errorLog.textContent += message + '\n\n';
        });
        
        import('./main.js').then(() => {
            console.log('Main.js loaded successfully');
            errorLog.textContent += 'Main.js loaded successfully\n';
        }).catch(err => {
            console.error('Failed to load main.js:', err);
            errorLog.textContent += `Failed to load main.js: ${err}\n`;
        });
    </script>
</body>
</html>