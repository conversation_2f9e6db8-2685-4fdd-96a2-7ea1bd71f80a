# Guide Claude Plan Max

## ✅ Configuration terminée !

La commande `claude` ouvre maintenant Claude.ai dans ton navigateur, utilisant ton abonnement Plan Max au lieu de l'API.

## 🚀 Comment utiliser

### 1. Dans le terminal
```powershell
claude
```
Ouvre directement Claude dans ton navigateur

### 2. <PERSON> (Recommandé)
Lance `installer-claude-desktop.bat` ou va sur https://claude.ai/download

Avantages :
- Application native Windows
- Plus rapide que le navigateur
- Raccourcis clavier
- Notifications
- Utilise ton Plan Max

### 3. Raccourcis pratiques

#### Créer un raccourci bureau
- Clic droit sur le bureau → Nouveau → Raccourci
- Entrer : `https://claude.ai/new`
- Nom : Claude

#### Épingler à la barre des tâches
- Ouvrir Claude dans Chrome/Edge
- Menu ⋮ → Plus d'outils → Créer un raccourci
- Cocher "Ouvrir dans une fenêtre"

## 💡 Avantages de ton Plan Max

✅ **Pas de limite de crédits** (contrairement à l'API)
✅ **Accès à Claude Opus 4** (le plus puissant)
✅ **Projets illimités**
✅ **Historique complet**
✅ **Priorité sur les serveurs**

## 🔧 Résumé des changements

1. ❌ Supprimé le package qui ouvrait VSCode
2. ❌ Remplacé le script API (qui demandait des crédits)
3. ✅ Configuré `claude` pour ouvrir le web
4. ✅ Utilise maintenant ton Plan Max

## 📱 Bonus : Applications mobiles

- **iOS** : https://apps.apple.com/app/claude-by-anthropic/
- **Android** : https://play.google.com/store/apps/details?id=com.anthropic.claude

Synchronisation automatique avec ton compte Plan Max !