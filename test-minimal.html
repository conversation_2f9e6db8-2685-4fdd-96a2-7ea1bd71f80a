<!DOCTYPE html>
<html>
<head>
    <title>Minimal Test</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
</head>
<body>
    <script>
        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            scene: {
                preload: preload,
                create: create
            }
        };

        function preload() {
            console.log('Preload started');
            // Create a simple colored rectangle as a test
            this.load.image('test', 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==');
        }

        function create() {
            console.log('Create started');
            this.add.text(400, 300, 'Phaser is working!', { 
                fontSize: '32px', 
                color: '#fff' 
            }).setOrigin(0.5);
        }

        const game = new Phaser.Game(config);
        console.log('Game created');
    </script>
</body>
</html>