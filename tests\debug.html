<!DOCTYPE html>
<html>
<head>
    <title>Debug Test</title>
    <style>
        body {
            background: #222;
            color: white;
            font-family: monospace;
            padding: 20px;
        }
        #log {
            white-space: pre-wrap;
            background: #111;
            padding: 10px;
            border: 1px solid #444;
            min-height: 400px;
        }
    </style>
</head>
<body>
    <h1>Debug Test</h1>
    <div id="log"></div>
    
    <script>
        const log = document.getElementById('log');
        
        function addLog(msg) {
            log.textContent += msg + '\n';
            console.log(msg);
        }
        
        window.onerror = function(msg, url, line, col, error) {
            addLog(`ERROR: ${msg}\nFile: ${url}\nLine: ${line}\nCol: ${col}\n${error?.stack || ''}`);
            return false;
        };
        
        addLog('Starting test...');
        
        // Test 1: Check if modules can load
        addLog('Loading modules...');
        
        import('./skills.js').then(() => {
            addLog('✓ skills.js loaded');
            return import('./pets.js');
        }).then(() => {
            addLog('✓ pets.js loaded');
            return import('./CombatEngine.js');
        }).then(() => {
            addLog('✓ CombatEngine.js loaded');
            return import('./UIManager.js');
        }).then(() => {
            addLog('✓ UIManager.js loaded');
            return import('./FightScene.js');
        }).then(() => {
            addLog('✓ FightScene.js loaded');
            return import('./BootScene.js');
        }).then(() => {
            addLog('✓ BootScene.js loaded');
            return import('./main.js');
        }).then(() => {
            addLog('✓ main.js loaded');
            addLog('SUCCESS: All modules loaded!');
        }).catch(err => {
            addLog(`FAILED: ${err}\n${err.stack}`);
        });
    </script>
</body>
</html>