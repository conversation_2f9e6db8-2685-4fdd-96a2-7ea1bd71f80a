# Script pour renommer le dossier du projet
$oldPath = "C:\Users\<USER>\OneDrive\Documents\CODES\__ROSEBUD-AI-LABRUTE\LaBrute Reborn - Arena Combat-Strengthen guaranteed hit and dodge feedback systems (1)"
$newPath = "C:\Users\<USER>\OneDrive\Documents\CODES\__ROSEBUD-AI-LABRUTE\labrute-reborn"

Write-Host "Renommage du projet..." -ForegroundColor Yellow
Write-Host "Ancien nom: $oldPath" -ForegroundColor Gray
Write-Host "Nouveau nom: $newPath" -ForegroundColor Green

try {
    # Vérifier si le nouveau dossier existe déjà
    if (Test-Path $newPath) {
        Write-Host "ERREUR: Le dossier de destination existe déjà!" -ForegroundColor Red
        exit 1
    }
    
    # Renommer le dossier
    Rename-Item -Path $oldPath -NewName "labrute-reborn" -Force
    
    Write-Host "`nDossier renommé avec succès!" -ForegroundColor Green
    Write-Host "Le projet est maintenant dans: $newPath" -ForegroundColor Cyan
    
    # Ouvrir le nouveau dossier
    Set-Location $newPath
    Write-Host "`nVous êtes maintenant dans le nouveau dossier." -ForegroundColor Yellow
    
} catch {
    Write-Host "ERREUR lors du renommage: $_" -ForegroundColor Red
    exit 1
}